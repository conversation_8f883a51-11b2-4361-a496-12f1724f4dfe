[{"name": "Process Start", "start": 1748259754525, "end": 1748259759166, "duration": 4641, "pid": 28492, "index": 0}, {"name": "Application Start", "start": 1748259759168, "end": 1748259761128, "duration": 1960, "pid": 28492, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748259759190, "end": 1748259759234, "duration": 44, "pid": 28492, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748259759234, "end": 1748259759289, "duration": 55, "pid": 28492, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748259759235, "end": 1748259759235, "duration": 0, "pid": 28492, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748259759238, "end": 1748259759238, "duration": 0, "pid": 28492, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748259759240, "end": 1748259759241, "duration": 1, "pid": 28492, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748259759241, "end": 1748259759242, "duration": 1, "pid": 28492, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748259759243, "end": 1748259759244, "duration": 1, "pid": 28492, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748259759245, "end": 1748259759246, "duration": 1, "pid": 28492, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748259759247, "end": 1748259759247, "duration": 0, "pid": 28492, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748259759248, "end": 1748259759249, "duration": 1, "pid": 28492, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748259759249, "end": 1748259759250, "duration": 1, "pid": 28492, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748259759251, "end": 1748259759251, "duration": 0, "pid": 28492, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748259759252, "end": 1748259759253, "duration": 1, "pid": 28492, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748259759254, "end": 1748259759254, "duration": 0, "pid": 28492, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748259759256, "end": 1748259759256, "duration": 0, "pid": 28492, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748259759257, "end": 1748259759257, "duration": 0, "pid": 28492, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748259759258, "end": 1748259759259, "duration": 1, "pid": 28492, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748259759259, "end": 1748259759260, "duration": 1, "pid": 28492, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748259759261, "end": 1748259759261, "duration": 0, "pid": 28492, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748259759263, "end": 1748259759264, "duration": 1, "pid": 28492, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748259759264, "end": 1748259759265, "duration": 1, "pid": 28492, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748259759266, "end": 1748259759266, "duration": 0, "pid": 28492, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748259759268, "end": 1748259759268, "duration": 0, "pid": 28492, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748259759271, "end": 1748259759271, "duration": 0, "pid": 28492, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748259759273, "end": 1748259759274, "duration": 1, "pid": 28492, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748259759276, "end": 1748259759276, "duration": 0, "pid": 28492, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748259759281, "end": 1748259759282, "duration": 1, "pid": 28492, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748259759287, "end": 1748259759288, "duration": 1, "pid": 28492, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748259759288, "end": 1748259759288, "duration": 0, "pid": 28492, "index": 30}, {"name": "Load extend/agent.js", "start": 1748259759290, "end": 1748259759424, "duration": 134, "pid": 28492, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/agent.js", "start": 1748259759292, "end": 1748259759296, "duration": 4, "pid": 28492, "index": 32}, {"name": "Require(28) node_modules/egg-schedule/app/extend/agent.js", "start": 1748259759299, "end": 1748259759405, "duration": 106, "pid": 28492, "index": 33}, {"name": "Require(29) node_modules/egg-logrotator/app/extend/agent.js", "start": 1748259759406, "end": 1748259759409, "duration": 3, "pid": 28492, "index": 34}, {"name": "Load extend/context.js", "start": 1748259759424, "end": 1748259759564, "duration": 140, "pid": 28492, "index": 35}, {"name": "Require(30) node_modules/egg-security/app/extend/context.js", "start": 1748259759425, "end": 1748259759484, "duration": 59, "pid": 28492, "index": 36}, {"name": "Require(31) node_modules/egg-jsonp/app/extend/context.js", "start": 1748259759485, "end": 1748259759491, "duration": 6, "pid": 28492, "index": 37}, {"name": "Require(32) node_modules/egg-i18n/app/extend/context.js", "start": 1748259759492, "end": 1748259759493, "duration": 1, "pid": 28492, "index": 38}, {"name": "Require(33) node_modules/egg-multipart/app/extend/context.js", "start": 1748259759495, "end": 1748259759538, "duration": 43, "pid": 28492, "index": 39}, {"name": "Require(34) node_modules/egg-view/app/extend/context.js", "start": 1748259759540, "end": 1748259759544, "duration": 4, "pid": 28492, "index": 40}, {"name": "Require(35) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748259759547, "end": 1748259759549, "duration": 2, "pid": 28492, "index": 41}, {"name": "Require(36) node_modules/egg/app/extend/context.js", "start": 1748259759551, "end": 1748259759556, "duration": 5, "pid": 28492, "index": 42}, {"name": "Load agent.js", "start": 1748259759565, "end": 1748259759656, "duration": 91, "pid": 28492, "index": 43}, {"name": "Require(37) node_modules/egg-security/agent.js", "start": 1748259759566, "end": 1748259759567, "duration": 1, "pid": 28492, "index": 44}, {"name": "Require(38) node_modules/egg-onerror/agent.js", "start": 1748259759568, "end": 1748259759568, "duration": 0, "pid": 28492, "index": 45}, {"name": "Require(39) node_modules/egg-watcher/agent.js", "start": 1748259759569, "end": 1748259759589, "duration": 20, "pid": 28492, "index": 46}, {"name": "Require(40) node_modules/egg-schedule/agent.js", "start": 1748259759590, "end": 1748259759592, "duration": 2, "pid": 28492, "index": 47}, {"name": "Require(41) node_modules/egg-development/agent.js", "start": 1748259759593, "end": 1748259759618, "duration": 25, "pid": 28492, "index": 48}, {"name": "Require(42) node_modules/egg-logrotator/agent.js", "start": 1748259759618, "end": 1748259759619, "duration": 1, "pid": 28492, "index": 49}, {"name": "Require(43) node_modules/egg-sequelize/agent.js", "start": 1748259759620, "end": 1748259759621, "duration": 1, "pid": 28492, "index": 50}, {"name": "Require(44) node_modules/egg-mysql/agent.js", "start": 1748259759622, "end": 1748259759644, "duration": 22, "pid": 28492, "index": 51}, {"name": "Require(45) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/agent.js", "start": 1748259759646, "end": 1748259759654, "duration": 8, "pid": 28492, "index": 52}, {"name": "Require(46) node_modules/egg/agent.js", "start": 1748259759655, "end": 1748259759655, "duration": 0, "pid": 28492, "index": 53}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748259759663, "end": 1748259760870, "duration": 1207, "pid": 28492, "index": 54}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1748259759665, "end": 1748259760826, "duration": 1161, "pid": 28492, "index": 55}, {"name": "Before Start in node_modules/egg-development/agent.js:9:9", "start": 1748259759665, "end": 1748259761105, "duration": 1440, "pid": 28492, "index": 56}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748259760499, "end": 1748259760616, "duration": 117, "pid": 28492, "index": 57}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748259760529, "end": 1748259760865, "duration": 336, "pid": 28492, "index": 58}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748259760651, "end": 1748259761127, "duration": 476, "pid": 28492, "index": 59}]