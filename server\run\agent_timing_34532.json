[{"name": "Process Start", "start": 1748259624892, "end": 1748259631356, "duration": 6464, "pid": 34532, "index": 0}, {"name": "Application Start", "start": 1748259631358, "end": 1748259635699, "duration": 4341, "pid": 34532, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748259631392, "end": 1748259631459, "duration": 67, "pid": 34532, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748259631459, "end": 1748259631607, "duration": 148, "pid": 34532, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748259631460, "end": 1748259631461, "duration": 1, "pid": 34532, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748259631465, "end": 1748259631466, "duration": 1, "pid": 34532, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748259631467, "end": 1748259631469, "duration": 2, "pid": 34532, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748259631471, "end": 1748259631472, "duration": 1, "pid": 34532, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748259631474, "end": 1748259631476, "duration": 2, "pid": 34532, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748259631478, "end": 1748259631480, "duration": 2, "pid": 34532, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748259631482, "end": 1748259631482, "duration": 0, "pid": 34532, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748259631483, "end": 1748259631485, "duration": 2, "pid": 34532, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748259631487, "end": 1748259631488, "duration": 1, "pid": 34532, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748259631490, "end": 1748259631491, "duration": 1, "pid": 34532, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748259631493, "end": 1748259631494, "duration": 1, "pid": 34532, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748259631495, "end": 1748259631496, "duration": 1, "pid": 34532, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748259631497, "end": 1748259631497, "duration": 0, "pid": 34532, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748259631498, "end": 1748259631499, "duration": 1, "pid": 34532, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748259631500, "end": 1748259631502, "duration": 2, "pid": 34532, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748259631503, "end": 1748259631504, "duration": 1, "pid": 34532, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748259631505, "end": 1748259631506, "duration": 1, "pid": 34532, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748259631507, "end": 1748259631507, "duration": 0, "pid": 34532, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748259631509, "end": 1748259631510, "duration": 1, "pid": 34532, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748259631511, "end": 1748259631512, "duration": 1, "pid": 34532, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748259631513, "end": 1748259631514, "duration": 1, "pid": 34532, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748259631589, "end": 1748259631589, "duration": 0, "pid": 34532, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748259631590, "end": 1748259631591, "duration": 1, "pid": 34532, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748259631594, "end": 1748259631595, "duration": 1, "pid": 34532, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748259631600, "end": 1748259631601, "duration": 1, "pid": 34532, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748259631606, "end": 1748259631607, "duration": 1, "pid": 34532, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748259631607, "end": 1748259631607, "duration": 0, "pid": 34532, "index": 30}, {"name": "Load extend/agent.js", "start": 1748259631609, "end": 1748259631772, "duration": 163, "pid": 34532, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/agent.js", "start": 1748259631611, "end": 1748259631616, "duration": 5, "pid": 34532, "index": 32}, {"name": "Require(28) node_modules/egg-schedule/app/extend/agent.js", "start": 1748259631622, "end": 1748259631751, "duration": 129, "pid": 34532, "index": 33}, {"name": "Require(29) node_modules/egg-logrotator/app/extend/agent.js", "start": 1748259631754, "end": 1748259631759, "duration": 5, "pid": 34532, "index": 34}, {"name": "Load extend/context.js", "start": 1748259631773, "end": 1748259631907, "duration": 134, "pid": 34532, "index": 35}, {"name": "Require(30) node_modules/egg-security/app/extend/context.js", "start": 1748259631774, "end": 1748259631807, "duration": 33, "pid": 34532, "index": 36}, {"name": "Require(31) node_modules/egg-jsonp/app/extend/context.js", "start": 1748259631809, "end": 1748259631819, "duration": 10, "pid": 34532, "index": 37}, {"name": "Require(32) node_modules/egg-i18n/app/extend/context.js", "start": 1748259631822, "end": 1748259631824, "duration": 2, "pid": 34532, "index": 38}, {"name": "Require(33) node_modules/egg-multipart/app/extend/context.js", "start": 1748259631826, "end": 1748259631876, "duration": 50, "pid": 34532, "index": 39}, {"name": "Require(34) node_modules/egg-view/app/extend/context.js", "start": 1748259631878, "end": 1748259631885, "duration": 7, "pid": 34532, "index": 40}, {"name": "Require(35) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748259631888, "end": 1748259631890, "duration": 2, "pid": 34532, "index": 41}, {"name": "Require(36) node_modules/egg/app/extend/context.js", "start": 1748259631892, "end": 1748259631896, "duration": 4, "pid": 34532, "index": 42}, {"name": "Load agent.js", "start": 1748259631907, "end": 1748259632015, "duration": 108, "pid": 34532, "index": 43}, {"name": "Require(37) node_modules/egg-security/agent.js", "start": 1748259631908, "end": 1748259631909, "duration": 1, "pid": 34532, "index": 44}, {"name": "Require(38) node_modules/egg-onerror/agent.js", "start": 1748259631910, "end": 1748259631911, "duration": 1, "pid": 34532, "index": 45}, {"name": "Require(39) node_modules/egg-watcher/agent.js", "start": 1748259631912, "end": 1748259631934, "duration": 22, "pid": 34532, "index": 46}, {"name": "Require(40) node_modules/egg-schedule/agent.js", "start": 1748259631935, "end": 1748259631938, "duration": 3, "pid": 34532, "index": 47}, {"name": "Require(41) node_modules/egg-development/agent.js", "start": 1748259631939, "end": 1748259631967, "duration": 28, "pid": 34532, "index": 48}, {"name": "Require(42) node_modules/egg-logrotator/agent.js", "start": 1748259631968, "end": 1748259631969, "duration": 1, "pid": 34532, "index": 49}, {"name": "Require(43) node_modules/egg-sequelize/agent.js", "start": 1748259631971, "end": 1748259631971, "duration": 0, "pid": 34532, "index": 50}, {"name": "Require(44) node_modules/egg-mysql/agent.js", "start": 1748259631974, "end": 1748259631999, "duration": 25, "pid": 34532, "index": 51}, {"name": "Require(45) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/agent.js", "start": 1748259632000, "end": 1748259632012, "duration": 12, "pid": 34532, "index": 52}, {"name": "Require(46) node_modules/egg/agent.js", "start": 1748259632013, "end": 1748259632013, "duration": 0, "pid": 34532, "index": 53}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748259632028, "end": 1748259635245, "duration": 3217, "pid": 34532, "index": 54}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1748259632029, "end": 1748259635146, "duration": 3117, "pid": 34532, "index": 55}, {"name": "Before Start in node_modules/egg-development/agent.js:9:9", "start": 1748259632030, "end": 1748259635667, "duration": 3637, "pid": 34532, "index": 56}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748259634456, "end": 1748259634834, "duration": 378, "pid": 34532, "index": 57}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748259634571, "end": 1748259635237, "duration": 666, "pid": 34532, "index": 58}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748259634916, "end": 1748259635691, "duration": 775, "pid": 34532, "index": 59}]