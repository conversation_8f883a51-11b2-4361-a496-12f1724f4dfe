[{"name": "Process Start", "start": 1748260999202, "end": 1748261009379, "duration": 10177, "pid": 9532, "index": 0}, {"name": "Application Start", "start": 1748261009388, "end": 1748261014072, "duration": 4684, "pid": 9532, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748261009456, "end": 1748261009575, "duration": 119, "pid": 9532, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748261009575, "end": 1748261009757, "duration": 182, "pid": 9532, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748261009580, "end": 1748261009582, "duration": 2, "pid": 9532, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748261009590, "end": 1748261009591, "duration": 1, "pid": 9532, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748261009593, "end": 1748261009595, "duration": 2, "pid": 9532, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748261009596, "end": 1748261009599, "duration": 3, "pid": 9532, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748261009603, "end": 1748261009606, "duration": 3, "pid": 9532, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748261009608, "end": 1748261009610, "duration": 2, "pid": 9532, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748261009612, "end": 1748261009615, "duration": 3, "pid": 9532, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748261009619, "end": 1748261009622, "duration": 3, "pid": 9532, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748261009624, "end": 1748261009626, "duration": 2, "pid": 9532, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748261009629, "end": 1748261009631, "duration": 2, "pid": 9532, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748261009634, "end": 1748261009635, "duration": 1, "pid": 9532, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748261009656, "end": 1748261009658, "duration": 2, "pid": 9532, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748261009660, "end": 1748261009661, "duration": 1, "pid": 9532, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748261009664, "end": 1748261009666, "duration": 2, "pid": 9532, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748261009669, "end": 1748261009670, "duration": 1, "pid": 9532, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748261009672, "end": 1748261009674, "duration": 2, "pid": 9532, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748261009677, "end": 1748261009678, "duration": 1, "pid": 9532, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748261009685, "end": 1748261009687, "duration": 2, "pid": 9532, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748261009689, "end": 1748261009691, "duration": 2, "pid": 9532, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748261009694, "end": 1748261009695, "duration": 1, "pid": 9532, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748261009696, "end": 1748261009698, "duration": 2, "pid": 9532, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748261009712, "end": 1748261009712, "duration": 0, "pid": 9532, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748261009724, "end": 1748261009725, "duration": 1, "pid": 9532, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748261009729, "end": 1748261009730, "duration": 1, "pid": 9532, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748261009739, "end": 1748261009740, "duration": 1, "pid": 9532, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748261009755, "end": 1748261009756, "duration": 1, "pid": 9532, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748261009756, "end": 1748261009756, "duration": 0, "pid": 9532, "index": 30}, {"name": "Load extend/agent.js", "start": 1748261009760, "end": 1748261010176, "duration": 416, "pid": 9532, "index": 31}, {"name": "Require(27) node_modules/egg-security/app/extend/agent.js", "start": 1748261009763, "end": 1748261009800, "duration": 37, "pid": 9532, "index": 32}, {"name": "Require(28) node_modules/egg-schedule/app/extend/agent.js", "start": 1748261009806, "end": 1748261010097, "duration": 291, "pid": 9532, "index": 33}, {"name": "Require(29) node_modules/egg-logrotator/app/extend/agent.js", "start": 1748261010102, "end": 1748261010126, "duration": 24, "pid": 9532, "index": 34}, {"name": "Load extend/context.js", "start": 1748261010176, "end": 1748261010662, "duration": 486, "pid": 9532, "index": 35}, {"name": "Require(30) node_modules/egg-security/app/extend/context.js", "start": 1748261010179, "end": 1748261010419, "duration": 240, "pid": 9532, "index": 36}, {"name": "Require(31) node_modules/egg-jsonp/app/extend/context.js", "start": 1748261010420, "end": 1748261010453, "duration": 33, "pid": 9532, "index": 37}, {"name": "Require(32) node_modules/egg-i18n/app/extend/context.js", "start": 1748261010454, "end": 1748261010455, "duration": 1, "pid": 9532, "index": 38}, {"name": "Require(33) node_modules/egg-multipart/app/extend/context.js", "start": 1748261010457, "end": 1748261010608, "duration": 151, "pid": 9532, "index": 39}, {"name": "Require(34) node_modules/egg-view/app/extend/context.js", "start": 1748261010612, "end": 1748261010625, "duration": 13, "pid": 9532, "index": 40}, {"name": "Require(35) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748261010630, "end": 1748261010634, "duration": 4, "pid": 9532, "index": 41}, {"name": "Require(36) node_modules/egg/app/extend/context.js", "start": 1748261010636, "end": 1748261010643, "duration": 7, "pid": 9532, "index": 42}, {"name": "Load agent.js", "start": 1748261010662, "end": 1748261010911, "duration": 249, "pid": 9532, "index": 43}, {"name": "Require(37) node_modules/egg-security/agent.js", "start": 1748261010664, "end": 1748261010668, "duration": 4, "pid": 9532, "index": 44}, {"name": "Require(38) node_modules/egg-onerror/agent.js", "start": 1748261010674, "end": 1748261010675, "duration": 1, "pid": 9532, "index": 45}, {"name": "Require(39) node_modules/egg-watcher/agent.js", "start": 1748261010681, "end": 1748261010727, "duration": 46, "pid": 9532, "index": 46}, {"name": "Require(40) node_modules/egg-schedule/agent.js", "start": 1748261010728, "end": 1748261010737, "duration": 9, "pid": 9532, "index": 47}, {"name": "Require(41) node_modules/egg-development/agent.js", "start": 1748261010739, "end": 1748261010836, "duration": 97, "pid": 9532, "index": 48}, {"name": "Require(42) node_modules/egg-logrotator/agent.js", "start": 1748261010837, "end": 1748261010838, "duration": 1, "pid": 9532, "index": 49}, {"name": "Require(43) node_modules/egg-sequelize/agent.js", "start": 1748261010841, "end": 1748261010842, "duration": 1, "pid": 9532, "index": 50}, {"name": "Require(44) node_modules/egg-mysql/agent.js", "start": 1748261010845, "end": 1748261010894, "duration": 49, "pid": 9532, "index": 51}, {"name": "Require(45) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/agent.js", "start": 1748261010895, "end": 1748261010909, "duration": 14, "pid": 9532, "index": 52}, {"name": "Require(46) node_modules/egg/agent.js", "start": 1748261010909, "end": 1748261010910, "duration": 1, "pid": 9532, "index": 53}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748261010925, "end": 1748261013562, "duration": 2637, "pid": 9532, "index": 54}, {"name": "Before Start in node_modules/egg-schedule/agent.js:12:9", "start": 1748261010926, "end": 1748261013504, "duration": 2578, "pid": 9532, "index": 55}, {"name": "Before Start in node_modules/egg-development/agent.js:9:9", "start": 1748261010926, "end": 1748261014053, "duration": 3127, "pid": 9532, "index": 56}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748261012861, "end": 1748261013205, "duration": 344, "pid": 9532, "index": 57}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748261012925, "end": 1748261013556, "duration": 631, "pid": 9532, "index": 58}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748261013275, "end": 1748261014071, "duration": 796, "pid": 9532, "index": 59}]