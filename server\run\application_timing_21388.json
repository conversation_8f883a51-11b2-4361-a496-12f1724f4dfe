[{"name": "Process Start", "start": 1748259761370, "end": 1748259764149, "duration": 2779, "pid": 21388, "index": 0}, {"name": "Application Start", "start": 1748259764151, "end": 1748259768615, "duration": 4464, "pid": 21388, "index": 1}, {"name": "<PERSON><PERSON>", "start": 1748259764180, "end": 1748259764235, "duration": 55, "pid": 21388, "index": 2}, {"name": "<PERSON><PERSON> Config", "start": 1748259764235, "end": 1748259764302, "duration": 67, "pid": 21388, "index": 3}, {"name": "Require(0) config/config.default.js", "start": 1748259764236, "end": 1748259764237, "duration": 1, "pid": 21388, "index": 4}, {"name": "Require(1) config/config.local.js", "start": 1748259764239, "end": 1748259764240, "duration": 1, "pid": 21388, "index": 5}, {"name": "Require(2) node_modules/egg-session/config/config.default.js", "start": 1748259764241, "end": 1748259764246, "duration": 5, "pid": 21388, "index": 6}, {"name": "Require(3) node_modules/egg-security/config/config.default.js", "start": 1748259764247, "end": 1748259764247, "duration": 0, "pid": 21388, "index": 7}, {"name": "Require(4) node_modules/egg-jsonp/config/config.default.js", "start": 1748259764249, "end": 1748259764251, "duration": 2, "pid": 21388, "index": 8}, {"name": "Require(5) node_modules/egg-onerror/config/config.default.js", "start": 1748259764252, "end": 1748259764252, "duration": 0, "pid": 21388, "index": 9}, {"name": "Require(6) node_modules/egg-i18n/config/config.default.js", "start": 1748259764253, "end": 1748259764253, "duration": 0, "pid": 21388, "index": 10}, {"name": "Require(7) node_modules/egg-watcher/config/config.default.js", "start": 1748259764254, "end": 1748259764255, "duration": 1, "pid": 21388, "index": 11}, {"name": "Require(8) node_modules/egg-schedule/config/config.default.js", "start": 1748259764256, "end": 1748259764257, "duration": 1, "pid": 21388, "index": 12}, {"name": "Require(9) node_modules/egg-multipart/config/config.default.js", "start": 1748259764258, "end": 1748259764259, "duration": 1, "pid": 21388, "index": 13}, {"name": "Require(10) node_modules/egg-development/config/config.default.js", "start": 1748259764261, "end": 1748259764262, "duration": 1, "pid": 21388, "index": 14}, {"name": "Require(11) node_modules/egg-logrotator/config/config.default.js", "start": 1748259764265, "end": 1748259764265, "duration": 0, "pid": 21388, "index": 15}, {"name": "Require(12) node_modules/egg-static/config/config.default.js", "start": 1748259764266, "end": 1748259764267, "duration": 1, "pid": 21388, "index": 16}, {"name": "Require(13) node_modules/egg-view/config/config.default.js", "start": 1748259764268, "end": 1748259764269, "duration": 1, "pid": 21388, "index": 17}, {"name": "Require(14) node_modules/egg-sequelize/config/config.default.js", "start": 1748259764270, "end": 1748259764271, "duration": 1, "pid": 21388, "index": 18}, {"name": "Require(15) node_modules/egg-jwt/config/config.default.js", "start": 1748259764272, "end": 1748259764272, "duration": 0, "pid": 21388, "index": 19}, {"name": "Require(16) node_modules/egg-cors/config/config.default.js", "start": 1748259764275, "end": 1748259764276, "duration": 1, "pid": 21388, "index": 20}, {"name": "Require(17) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/config/config.default.js", "start": 1748259764277, "end": 1748259764278, "duration": 1, "pid": 21388, "index": 21}, {"name": "Require(18) node_modules/egg-mysql/config/config.default.js", "start": 1748259764280, "end": 1748259764280, "duration": 0, "pid": 21388, "index": 22}, {"name": "Require(19) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/config/config.default.js", "start": 1748259764282, "end": 1748259764282, "duration": 0, "pid": 21388, "index": 23}, {"name": "Require(20) node_modules/egg/config/config.default.js", "start": 1748259764283, "end": 1748259764285, "duration": 2, "pid": 21388, "index": 24}, {"name": "Require(21) config/config.default.js", "start": 1748259764287, "end": 1748259764287, "duration": 0, "pid": 21388, "index": 25}, {"name": "Require(22) node_modules/egg-security/config/config.local.js", "start": 1748259764289, "end": 1748259764290, "duration": 1, "pid": 21388, "index": 26}, {"name": "Require(23) node_modules/egg-watcher/config/config.local.js", "start": 1748259764292, "end": 1748259764293, "duration": 1, "pid": 21388, "index": 27}, {"name": "Require(24) node_modules/egg-view/config/config.local.js", "start": 1748259764297, "end": 1748259764298, "duration": 1, "pid": 21388, "index": 28}, {"name": "Require(25) node_modules/egg/config/config.local.js", "start": 1748259764301, "end": 1748259764302, "duration": 1, "pid": 21388, "index": 29}, {"name": "Require(26) config/config.local.js", "start": 1748259764302, "end": 1748259764302, "duration": 0, "pid": 21388, "index": 30}, {"name": "Load extend/application.js", "start": 1748259764304, "end": 1748259764441, "duration": 137, "pid": 21388, "index": 31}, {"name": "Require(27) node_modules/egg-session/app/extend/application.js", "start": 1748259764306, "end": 1748259764307, "duration": 1, "pid": 21388, "index": 32}, {"name": "Require(28) node_modules/egg-security/app/extend/application.js", "start": 1748259764308, "end": 1748259764311, "duration": 3, "pid": 21388, "index": 33}, {"name": "Require(29) node_modules/egg-jsonp/app/extend/application.js", "start": 1748259764312, "end": 1748259764321, "duration": 9, "pid": 21388, "index": 34}, {"name": "Require(30) node_modules/egg-schedule/app/extend/application.js", "start": 1748259764324, "end": 1748259764333, "duration": 9, "pid": 21388, "index": 35}, {"name": "Require(31) node_modules/egg-logrotator/app/extend/application.js", "start": 1748259764335, "end": 1748259764338, "duration": 3, "pid": 21388, "index": 36}, {"name": "Require(32) node_modules/egg-view/app/extend/application.js", "start": 1748259764339, "end": 1748259764344, "duration": 5, "pid": 21388, "index": 37}, {"name": "Require(33) node_modules/egg-jwt/app/extend/application.js", "start": 1748259764346, "end": 1748259764433, "duration": 87, "pid": 21388, "index": 38}, {"name": "Load extend/request.js", "start": 1748259764441, "end": 1748259764458, "duration": 17, "pid": 21388, "index": 39}, {"name": "Require(34) node_modules/egg/app/extend/request.js", "start": 1748259764449, "end": 1748259764451, "duration": 2, "pid": 21388, "index": 40}, {"name": "Load extend/response.js", "start": 1748259764458, "end": 1748259764481, "duration": 23, "pid": 21388, "index": 41}, {"name": "Require(35) node_modules/egg/app/extend/response.js", "start": 1748259764467, "end": 1748259764471, "duration": 4, "pid": 21388, "index": 42}, {"name": "Load extend/context.js", "start": 1748259764481, "end": 1748259764557, "duration": 76, "pid": 21388, "index": 43}, {"name": "Require(36) node_modules/egg-security/app/extend/context.js", "start": 1748259764482, "end": 1748259764503, "duration": 21, "pid": 21388, "index": 44}, {"name": "Require(37) node_modules/egg-jsonp/app/extend/context.js", "start": 1748259764503, "end": 1748259764506, "duration": 3, "pid": 21388, "index": 45}, {"name": "Require(38) node_modules/egg-i18n/app/extend/context.js", "start": 1748259764507, "end": 1748259764508, "duration": 1, "pid": 21388, "index": 46}, {"name": "Require(39) node_modules/egg-multipart/app/extend/context.js", "start": 1748259764509, "end": 1748259764536, "duration": 27, "pid": 21388, "index": 47}, {"name": "Require(40) node_modules/egg-view/app/extend/context.js", "start": 1748259764538, "end": 1748259764539, "duration": 1, "pid": 21388, "index": 48}, {"name": "Require(41) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app/extend/context.js", "start": 1748259764541, "end": 1748259764542, "duration": 1, "pid": 21388, "index": 49}, {"name": "Require(42) node_modules/egg/app/extend/context.js", "start": 1748259764544, "end": 1748259764549, "duration": 5, "pid": 21388, "index": 50}, {"name": "Load extend/helper.js", "start": 1748259764557, "end": 1748259764607, "duration": 50, "pid": 21388, "index": 51}, {"name": "Require(43) node_modules/egg-security/app/extend/helper.js", "start": 1748259764558, "end": 1748259764590, "duration": 32, "pid": 21388, "index": 52}, {"name": "Require(44) node_modules/egg/app/extend/helper.js", "start": 1748259764596, "end": 1748259764597, "duration": 1, "pid": 21388, "index": 53}, {"name": "Require(45) app/extend/helper.js", "start": 1748259764598, "end": 1748259764599, "duration": 1, "pid": 21388, "index": 54}, {"name": "Load app.js", "start": 1748259764608, "end": 1748259764722, "duration": 114, "pid": 21388, "index": 55}, {"name": "Require(46) node_modules/egg-session/app.js", "start": 1748259764609, "end": 1748259764609, "duration": 0, "pid": 21388, "index": 56}, {"name": "Require(47) node_modules/egg-security/app.js", "start": 1748259764611, "end": 1748259764615, "duration": 4, "pid": 21388, "index": 57}, {"name": "Require(48) node_modules/egg-onerror/app.js", "start": 1748259764617, "end": 1748259764638, "duration": 21, "pid": 21388, "index": 58}, {"name": "Require(49) node_modules/egg-i18n/app.js", "start": 1748259764638, "end": 1748259764657, "duration": 19, "pid": 21388, "index": 59}, {"name": "Require(50) node_modules/egg-watcher/app.js", "start": 1748259764658, "end": 1748259764673, "duration": 15, "pid": 21388, "index": 60}, {"name": "Require(51) node_modules/egg-schedule/app.js", "start": 1748259764673, "end": 1748259764675, "duration": 2, "pid": 21388, "index": 61}, {"name": "Require(52) node_modules/egg-multipart/app.js", "start": 1748259764676, "end": 1748259764680, "duration": 4, "pid": 21388, "index": 62}, {"name": "Require(53) node_modules/egg-development/app.js", "start": 1748259764681, "end": 1748259764681, "duration": 0, "pid": 21388, "index": 63}, {"name": "Require(54) node_modules/egg-logrotator/app.js", "start": 1748259764682, "end": 1748259764683, "duration": 1, "pid": 21388, "index": 64}, {"name": "Require(55) node_modules/egg-static/app.js", "start": 1748259764683, "end": 1748259764684, "duration": 1, "pid": 21388, "index": 65}, {"name": "Require(56) node_modules/egg-sequelize/app.js", "start": 1748259764685, "end": 1748259764686, "duration": 1, "pid": 21388, "index": 66}, {"name": "Require(57) node_modules/egg-jwt/app.js", "start": 1748259764686, "end": 1748259764687, "duration": 1, "pid": 21388, "index": 67}, {"name": "Require(58) node_modules/egg-cors/app.js", "start": 1748259764687, "end": 1748259764688, "duration": 1, "pid": 21388, "index": 68}, {"name": "Require(59) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-validate/app.js", "start": 1748259764689, "end": 1748259764696, "duration": 7, "pid": 21388, "index": 69}, {"name": "Require(60) node_modules/egg-mysql/app.js", "start": 1748259764697, "end": 1748259764715, "duration": 18, "pid": 21388, "index": 70}, {"name": "Require(61) E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/app.js", "start": 1748259764715, "end": 1748259764720, "duration": 5, "pid": 21388, "index": 71}, {"name": "Before Start in node_modules/egg-watcher/lib/init.js:15:14", "start": 1748259764736, "end": 1748259768593, "duration": 3857, "pid": 21388, "index": 72}, {"name": "Load \"Symbol(model)\" to Application", "start": 1748259766115, "end": 1748259766555, "duration": 440, "pid": 21388, "index": 73}, {"name": "Before Start in app/model/index.js:5:7", "start": 1748259766246, "end": 1748259768511, "duration": 2265, "pid": 21388, "index": 74}, {"name": "Before Start in node_modules/egg-sequelize/lib/loader.js:39:7", "start": 1748259766654, "end": 1748259768614, "duration": 1960, "pid": 21388, "index": 75}, {"name": "Before Start in node_modules/egg-mysql/lib/mysql.js:14:7", "start": 1748259766928, "end": 1748259768590, "duration": 1662, "pid": 21388, "index": 76}, {"name": "Before Start in E:/桌面/HappyStockMarket/stock-analysis-web/node_modules/egg-redis/lib/redis.js:58:7", "start": 1748259767465, "end": 1748259768542, "duration": 1077, "pid": 21388, "index": 77}, {"name": "Load Service", "start": 1748259767466, "end": 1748259767954, "duration": 488, "pid": 21388, "index": 78}, {"name": "Load \"service\" to Context", "start": 1748259767466, "end": 1748259767954, "duration": 488, "pid": 21388, "index": 79}, {"name": "Load Middleware", "start": 1748259767955, "end": 1748259768352, "duration": 397, "pid": 21388, "index": 80}, {"name": "Load \"middlewares\" to Application", "start": 1748259767955, "end": 1748259768332, "duration": 377, "pid": 21388, "index": 81}, {"name": "Load Controller", "start": 1748259768352, "end": 1748259768417, "duration": 65, "pid": 21388, "index": 82}, {"name": "Load \"controller\" to Application", "start": 1748259768352, "end": 1748259768417, "duration": 65, "pid": 21388, "index": 83}, {"name": "Load Router", "start": 1748259768417, "end": 1748259768426, "duration": 9, "pid": 21388, "index": 84}, {"name": "Require(62) app/router.js", "start": 1748259768418, "end": 1748259768419, "duration": 1, "pid": 21388, "index": 85}, {"name": "Before Start in node_modules/egg-core/lib/egg.js:328:10", "start": 1748259768421, "end": 1748259768511, "duration": 90, "pid": 21388, "index": 86}]